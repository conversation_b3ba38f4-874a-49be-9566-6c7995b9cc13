/* pages/history/gameHistory.wxss */

/* 页面根元素样式 - 强制覆盖全局样式 */
page {
  background-color: #f8f4e9 !important; /* 更改为与首页匹配的米色背景 */
  height: 100vh !important;
  width: 100vw !important;
  overflow: hidden !important;
  position: relative !important;
}

/* 背景麻将装饰元素 */
.bg-elements {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none; /* 不阻挡点击事件 */
}

.mahjong-tile {
  position: absolute;
  width: 80rpx;
  height: 100rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.15);
  transform: rotate(var(--rotation));
  opacity: 0.1;
  z-index: -1;
}

.mahjong-1 {
  --rotation: 15deg;
  top: 10%;
  left: 10%;
}

.mahjong-2 {
  --rotation: -10deg;
  top: 20%;
  right: 15%;
}

.mahjong-3 {
  --rotation: 5deg;
  bottom: 25%;
  left: 5%;
}

.mahjong-4 {
  --rotation: -20deg;
  bottom: 15%;
  right: 10%;
}

.container {
  min-height: 100vh !important;
  height: 100vh !important;
  width: 100vw !important;
  background-color: #f8f4e9 !important; /* 与首页匹配的米色背景 */
  position: relative !important;
  z-index: 1000 !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 自定义导航栏右侧操作按钮 */
.nav-action {
  width: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  font-size: 36rpx;
  font-weight: 600;
  color: #8B0000; /* 更改为深红色 */
}

/* 滚动内容区域 - 参考gameList页面 */
.scroll-content {
  width: 100%;
  box-sizing: border-box;
  background-color: #f8f4e9; /* 与首页匹配的米色背景 */
  position: relative;
  z-index: 1002;
}

.content {
  padding: 32rpx;
  padding-top: 16rpx; /* 减少顶部内边距 */
  padding-bottom: 120rpx; /* 确保底部有足够空间 */
  margin-top: 0 !important; /* 覆盖全局样式的 margin-top: 88px */
  flex: none !important; /* 覆盖全局样式的 flex: 1 */
  background-color: #f8f4e9; /* 与首页匹配的米色背景 */
  position: relative;
  z-index: 1003;
  min-height: calc(100vh - 200rpx);
}

/* 标题样式 */
.mahjong-section-title {
  position: relative;
  display: inline-block;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  border-radius: 8rpx;
  color: #8B0000;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.3);
  transform: rotate(-2deg);
  font-size: 32rpx;
  margin-bottom: 16rpx;
}

.section-title {
  margin-bottom: 24rpx;
}

/* 游戏战绩卡片 */
.game-summary-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 8rpx solid #d4af37; /* 金色边框 */
}

/* 玩家列表 */
.player-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.player-item {
  display: flex;
  align-items: center;
  padding: 16rpx;
  background-color: rgba(248, 244, 233, 0.6);
  border-radius: 12rpx;
  border: 1rpx solid rgba(212, 175, 55, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 80rpx; /* 固定高度以确保垂直居中 */
}

.player-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 34rpx;
  color: #ffffff;
  margin-right: 16rpx;
  flex-shrink: 0;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.15);
}

.player-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%; /* 确保填满父容器高度 */
}

.player-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.player-score {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: flex-end; /* 右对齐 */
  height: 100%; /* 确保垂直居中 */
  min-width: 100rpx; /* 确保有足够宽度 */
}

.player-score.positive {
  color: #52c41a;
}

.player-score.negative {
  color: #f5222d;
}

/* 历史记录部分 */
.history-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 8rpx solid #d4af37; /* 金色边框 */
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.history-card {
  padding: 24rpx;
  background-color: rgba(248, 244, 233, 0.6);
  border-radius: 12rpx;
  border: 1rpx solid rgba(212, 175, 55, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.history-card:active {
  background-color: rgba(230, 196, 108, 0.2);
  transform: scale(0.98);
}

.round-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid rgba(212, 175, 55, 0.2);
}

.round-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #8B0000;
}

.round-time {
  font-size: 24rpx;
  color: #999;
}

.round-scores {
  display: flex;
  flex-direction: column;
}

/* 添加分割线，但最后一个项目不添加 */
.round-score-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  min-height: 60rpx; /* 设置最小高度 */
  position: relative; /* 为分割线定位 */
}

/* 分割线样式 */
.round-score-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1rpx;
  background-color: rgba(212, 175, 55, 0.2); /* 与其他分割线保持一致的颜色 */
}

.score-player-info {
  display: flex;
  align-items: center;
  height: 100%; /* 确保填满父容器高度 */
}

.score-player-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  color: #ffffff;
  margin-right: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  flex-shrink: 0; /* 防止缩小 */
}

.score-player-name {
  font-size: 26rpx;
  color: #333;
}

.score-value {
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: flex-end; /* 右对齐 */
  height: 100%; /* 确保垂直居中 */
  min-width: 80rpx; /* 确保有足够宽度 */
}

.score-value.positive {
  color: #52c41a;
}

.score-value.negative {
  color: #f5222d;
}

.score-unit {
  font-size: 24rpx;
  font-weight: 400;
  margin-left: 2rpx;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  text-align: center;
  border-left: 8rpx solid #d4af37; /* 金色边框 */
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #8B0000;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #666;
}

/* 选项菜单弹窗 */
.options-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
}

.options-menu {
  width: 75%;
  background-color: #ffffff;
  border-radius: 24rpx 0 0 24rpx;
  overflow: hidden;
  box-shadow: -8rpx 0 40rpx rgba(0, 0, 0, 0.2);
  margin-top: 120rpx;
  max-height: 70%;
  display: flex;
  flex-direction: column;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37); /* 金色渐变背景 */
}

.options-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #8B0000; /* 深红色 */
}

.options-close {
  font-size: 40rpx;
  color: #8B0000;
  padding: 0 10rpx;
}

.options-list {
  padding: 16rpx 0;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.option-item:last-child {
  border-bottom: none;
}

.option-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.option-text {
  font-size: 28rpx;
  color: #333;
}

.delete-text {
  color: #f5222d;
}

/* 轮次详情弹窗 */
.round-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 3000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.round-detail-card {
  width: 90%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(212, 175, 55, 0.18), 0 2rpx 8rpx rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx 16rpx 32rpx;
  background: linear-gradient(135deg, #e6c46c, #f8f4e9);
}

.detail-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #8B0000;
}

.detail-close {
  font-size: 40rpx;
  color: #8B0000;
  padding: 0 10rpx;
  cursor: pointer;
}

.detail-content {
  padding: 32rpx;
}

.detail-time {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 24rpx;
}

.detail-scores {
  margin-bottom: 24rpx;
}

.detail-score-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 60rpx; /* 设置最小高度 */
}

.detail-score-item:last-child {
  border-bottom: none;
}

.detail-player {
  display: flex;
  align-items: center;
  height: 100%; /* 确保填满父容器高度 */
}

.detail-avatar {
  width: 56rpx;
  height: 56rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #fff;
  margin-right: 16rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  box-shadow: 0 2rpx 8rpx rgba(212, 175, 55, 0.18);
  flex-shrink: 0; /* 防止缩小 */
}

.detail-name {
  font-size: 28rpx;
  color: #8B0000;
  font-weight: 500;
}

.detail-score {
  font-size: 36rpx;
  font-weight: 700;
  color: #8B0000;
  min-width: 80rpx;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end; /* 右对齐 */
  height: 100%; /* 确保垂直居中 */
}

.detail-score.positive {
  color: #52c41a;
}

.detail-score.negative {
  color: #f5222d;
}

.detail-actions {
  display: flex;
  justify-content: flex-end;
  gap: 24rpx;
  margin-top: 24rpx;
  padding: 0 32rpx 32rpx;
}

.btn {
  border-radius: 16rpx;
  font-size: 28rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border: none;
  padding: 0 30rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.btn-primary {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
}

.btn-outline {
  background-color: #ffffff;
  color: #8B0000;
  border: 1rpx solid #d4af37;
} 