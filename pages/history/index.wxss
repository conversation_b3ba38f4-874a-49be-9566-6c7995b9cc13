/* pages/history/index.wxss */
/**history.wxss**/

/* 页面根元素样式 - 与设计标准保持一致 */
page {
  background-color: #f8f4e9 !important; /* 统一米色背景 */
  height: auto !important;
  min-height: 100vh !important;
  overflow: auto !important;
  position: relative !important;
}

.container {
  min-height: 100vh;
  background-color: #f8f4e9;
  position: relative;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

/* 背景麻将装饰元素 - 与设计标准保持一致 */
.bg-elements {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.mahjong-tile {
  position: absolute;
  width: 60rpx;
  height: 80rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
  transform: rotate(var(--rotation));
  opacity: 0.05; /* 降低透明度，减少视觉干扰 */
  z-index: -1;
}

.mahjong-1 {
  --rotation: 15deg;
  top: 15%;
  left: 8%;
}

.mahjong-2 {
  --rotation: -10deg;
  top: 25%;
  right: 12%;
}

.mahjong-3 {
  --rotation: 5deg;
  bottom: 30%;
  left: 5%;
}

.mahjong-4 {
  --rotation: -20deg;
  bottom: 20%;
  right: 8%;
}

.status-bar {
  width: 100%;
  background-color: #ffffff;
}

.content {
  padding: 0 24rpx;
  padding-top: 20rpx; /* 调整顶部内边距 */
  padding-bottom: calc(110rpx + env(safe-area-inset-bottom)); /* 为底部导航栏预留空间 */
  box-sizing: border-box;
  overflow: hidden;
  margin-top: 0; /* 确保没有顶部外边距 */
}

/* 顶部占位元素 */
.top-spacer {
  height: 60rpx; /* 添加足够的高度作为顶部占位 */
  width: 100%;
}

.nav-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  height: 44px;
  background-color: #ffffff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 100;
  margin-bottom: 0;
}

.nav-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  width: 200rpx; /* 固定宽度确保居中 */
}

.nav-action {
  position: absolute;
  right: 24rpx;
  display: flex;
  justify-content: flex-end;
  padding: 10rpx 20rpx;
  z-index: 101; /* 确保在标题之上 */
}

.action-icon {
  font-size: 28rpx;
  color: #8B0000; /* 更改为深红色 */
}

/* 标题样式 */
.section-title {
  padding: 4rpx 0; /* 减少上下内边距 */
  margin-top: 30rpx; /* 调整顶部外边距 */
  margin-bottom: 24rpx; /* 增加底部外边距 */
  text-align: left; /* 改为左对齐 */
  padding-left: 32rpx; /* 增加左侧内边距，使左对齐更明显 */
  display: flex;
  justify-content: flex-start; /* 确保子元素左对齐 */
}

.mahjong-section-title {
  position: relative;
  display: inline-block;
  padding: 10rpx 24rpx; /* 进一步增加内边距 */
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  border-radius: 10rpx; /* 增加圆角 */
  color: #8B0000;
  font-weight: 600;
  box-shadow: 0 6rpx 16rpx rgba(212, 175, 55, 0.4); /* 增强阴影效果 */
  transform: rotate(-2deg);
  font-size: 36rpx; /* 进一步增加字体大小 */
  margin-left: 0; /* 确保没有左边距 */
  align-self: flex-start; /* 确保在flex容器中左对齐 */
}

/* 筛选条件指示器 */
.filter-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 24rpx; /* 进一步减少上下内边距 */
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  flex: 1;
}

.filter-tag {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
  font-size: 24rpx;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  margin-right: 16rpx;
  margin-bottom: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(212, 175, 55, 0.3);
}

.filter-tag-close {
  margin-left: 8rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.filter-reset {
  font-size: 24rpx;
  color: #8B0000;
  padding: 6rpx 16rpx;
}

.game-list {
  padding-top: 0; /* 移除顶部内边距 */
  margin-top: 0; /* 移除顶部外边距 */
}

.game-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 28rpx; /* 减少内边距 */
  margin-bottom: 12rpx; /* 进一步减少卡片间的间距 */
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-left: 8rpx solid #d4af37; /* 金色边框 */
}

.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx; /* 减少底部外边距 */
  padding-bottom: 10rpx; /* 减少底部内边距 */
  border-bottom: 1rpx solid #f0f0f0;
}

.game-name {
  font-size: 32rpx;
  font-weight: 500;
}

.game-time {
  font-size: 24rpx;
  color: #999;
}

/* 新增的纵向玩家列表样式 */
.game-players-vertical {
  margin-bottom: 16rpx;
}

.player-row {
  display: flex;
  align-items: center;
  padding: 8rpx 0; /* 减少上下内边距 */
}

.player-avatar {
  width: 60rpx; /* 略微减小头像尺寸 */
  height: 60rpx; /* 略微减小头像尺寸 */
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30rpx; /* 略微减小字体大小 */
  color: #ffffff;
  margin-right: 16rpx;
  flex-shrink: 0;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.15);
}

.player-name {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.player-score {
  font-size: 32rpx;
  font-weight: 600;
  min-width: 100rpx;
  text-align: right;
}

.player-score.positive {
  color: #52c41a;
}

.player-score.negative {
  color: #f5222d;
}

.game-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
  padding-top: 12rpx;
  border-top: 1rpx solid #f0f0f0;
}

.game-rounds {
  font-size: 24rpx;
  color: #999;
}

.game-action {
  display: flex;
  align-items: center;
}

.action-text {
  font-size: 28rpx;
  color: #8B0000;
  margin-right: 4rpx;
}

.action-icon {
  font-size: 36rpx;
  color: #8B0000;
}

/* 加载更多和没有更多数据 */
.load-more, .no-more {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 26rpx;
}

.load-btn {
  color: #8B0000;
  font-size: 28rpx;
  padding: 20rpx 40rpx;
  border: 1rpx solid #d4af37;
  border-radius: 16rpx;
  display: inline-block;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, rgba(230, 196, 108, 0.2), rgba(212, 175, 55, 0.2));
}

.load-btn:active {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 120rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #8B0000;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-subtext {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin: 0 40rpx;
}

/* 加载中状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

/* 筛选面板 */
.filter-panel-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.filter-panel {
  position: fixed;
  top: 0;
  right: -80%;
  width: 80%;
  height: 100%;
  background-color: #ffffff;
  z-index: 1001;
  box-shadow: -4rpx 0 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.filter-panel.show {
  right: 0;
}

.filter-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
}

.filter-panel-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #8B0000;
}

.filter-panel-close {
  font-size: 40rpx;
  color: #8B0000;
  padding: 10rpx;
}

.filter-section {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-section-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
}

.filter-option {
  padding: 16rpx 28rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 20rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
}

.filter-option.active {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  border-color: #d4af37;
  color: #8B0000;
}

.filter-date-range {
  display: flex;
  align-items: center;
}

.date-picker {
  flex: 1;
  padding: 20rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #999;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.date-picker.has-value {
  color: #333;
  border-color: #d4af37;
  background-color: rgba(230, 196, 108, 0.1);
}

.date-separator {
  margin: 0 16rpx;
  color: #999;
}

.filter-actions {
  padding: 24rpx;
  display: flex;
  justify-content: space-between;
  margin-top: auto;
}

.btn-reset, .btn-apply {
  flex: 1;
  margin: 0 8rpx;
  font-size: 28rpx;
  height: 80rpx;
  line-height: 80rpx;
}

.btn-reset {
  background-color: #f5f5f5;
  color: #666;
}

.btn-apply {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
  box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.3);
}