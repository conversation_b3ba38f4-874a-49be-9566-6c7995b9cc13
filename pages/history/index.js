// pages/history/index.js
// 引入API工具
const api = require('../../utils/api');
// 引入路由工具
const router = require('../../utils/router');

Page({

    /**
     * 页面的初始数据
     */
    data: {
        statusBarHeight: 20,
        navBarHeight: 44,
        contentHeight: 0,
        games: [],
        initialScore: 0, // 初始分数
        isLoading: false,
        page: 1,
        size: 10,
        hasMore: true,
        total: 0,
        // 筛选条件
        filterType: '',
        startDate: '',
        endDate: '',
        showFilter: false
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        // 获取系统信息设置状态栏高度
        const systemInfo = wx.getSystemInfoSync();
        const navBarHeight = 44; // 导航栏高度

        // 计算内容区域高度 - 增加更多高度以减少上方空隙
        const contentHeight = systemInfo.windowHeight - systemInfo.statusBarHeight - navBarHeight + 10;

        this.setData({
            statusBarHeight: systemInfo.statusBarHeight,
            navBarHeight: navBarHeight,
            contentHeight: contentHeight
        });
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        // 每次显示页面时重置并加载游戏列表
        this.setData({
            page: 1,
            games: [],
            hasMore: true
        });
        this.loadGames();
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {
        // 下拉刷新
        this.setData({
            page: 1,
            games: [],
            hasMore: true
        });
        this.loadGames();
        wx.stopPullDownRefresh();
    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {
        if (this.data.hasMore) {
            this.loadGames();
        }
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    },

    loadGames() {
        if (this.data.isLoading || !this.data.hasMore) return;

        this.setData({
            isLoading: true
        });

        // 显示加载中提示
        wx.showLoading({
            title: '加载中...',
        });

        // 构建查询参数
        const params = {
            settled: true, // 获取已结束/已结算的游戏记录
            page: this.data.page,
            size: this.data.size
        };

        // 添加筛选条件
        if (this.data.filterType) params.type = this.data.filterType;
        if (this.data.startDate) params.start_date = this.data.startDate;
        if (this.data.endDate) params.end_date = this.data.endDate;

        // 调用API获取已结束的游戏历史记录
        api.games.history(params).then(res => {
            wx.hideLoading();
            console.log('获取已结束游戏历史记录成功:', res);

            // 格式化游戏数据
            const newGames = res.data.list.map(game => {
                // 格式化时间
                const date = new Date(game.createTime);
                const formattedTime = `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}:${date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()}`;
                return {
                    ...game,
                    timeFormatted: formattedTime
                };
            });

            // 更新状态
            this.setData({
                games: this.data.page === 1 ? newGames : [...this.data.games, ...newGames],
                total: res.data.total,
                hasMore: this.data.page * this.data.size < res.data.total,
                page: this.data.page + 1,
                isLoading: false
            });
        }).catch(err => {
            wx.hideLoading();
            console.error('获取已结束游戏历史记录失败:', err);

            this.setData({
                isLoading: false
            });

            wx.showToast({
                title: '获取历史记录失败: ' + (err.message || '未知错误'),
                icon: 'none',
                duration: 2000
            });
        });
    },

    // 显示筛选面板
    showFilterPanel() {
        this.setData({
            showFilter: true
        });
    },

    // 隐藏筛选面板
    hideFilterPanel() {
        this.setData({
            showFilter: false
        });
    },

    // 选择游戏类型
    selectGameType(e) {
        const type = e.currentTarget.dataset.type;
        this.setData({
            filterType: type === this.data.filterType ? '' : type
        });
    },

    // 选择开始日期
    bindStartDateChange(e) {
        this.setData({
            startDate: e.detail.value
        });
    },

    // 选择结束日期
    bindEndDateChange(e) {
        this.setData({
            endDate: e.detail.value
        });
    },

    // 应用筛选
    applyFilter() {
        this.setData({
            page: 1,
            games: [],
            hasMore: true,
            showFilter: false
        });
        this.loadGames();
    },

    // 重置筛选
    resetFilter() {
        this.setData({
            filterType: '',
            startDate: '',
            endDate: '',
            page: 1,
            games: [],
            hasMore: true
        });
        this.loadGames();
        this.hideFilterPanel();
    },

    navigateToGame(e) {
        const gameId = e.currentTarget.dataset.id;
        wx.navigateTo({
            url: `/pages/history/gameHistory?id=${gameId}`
        }).then(() => {
            console.log('跳转到游戏历史页面成功');
        }).catch(err => {
            console.error('跳转失败:', err);
        });
    }
})