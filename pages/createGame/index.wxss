/**createGame.wxss**/
/* 覆盖全局样式，确保页面可以滚动 */
page {
  height: auto !important;
  background-color: #f8f4e9 !important; /* 与首页匹配的米色背景 */
}

.container {
  min-height: 100vh;
  background-color: #f8f4e9; /* 与首页匹配的米色背景 */
  /* 不使用flex布局，让内容自然流动 */
}

/* 背景麻将装饰元素 */
.bg-elements {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none; /* 不阻挡点击事件 */
}

.mahjong-tile {
  position: absolute;
  width: 80rpx;
  height: 100rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.15);
  transform: rotate(var(--rotation));
  opacity: 0.1;
  z-index: -1;
}

.mahjong-1 {
  --rotation: 15deg;
  top: 10%;
  left: 10%;
}

.mahjong-2 {
  --rotation: -10deg;
  top: 20%;
  right: 15%;
}

.mahjong-3 {
  --rotation: 5deg;
  bottom: 25%;
  left: 5%;
}

.mahjong-4 {
  --rotation: -20deg;
  bottom: 15%;
  right: 10%;
}

/* 重新定义content，确保不与全局样式冲突 */
.scroll-content {
  background-color: transparent;
  box-sizing: border-box;
}

.content {
  padding: 32rpx;
  margin-top: 0 !important; /* 覆盖全局样式的 margin-top: 88px */
  flex: none !important; /* 覆盖全局样式的 flex: 1 */
}

.form-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08), 0 2rpx 8rpx rgba(0,0,0,0.04);
  border-left: 8rpx solid #d4af37; /* 金色边框 */
}

.form-title {
  margin-bottom: 32rpx;
}

.mahjong-section-title {
  position: relative;
  display: inline-block;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  border-radius: 8rpx;
  color: #8B0000;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.3);
  transform: rotate(-2deg);
  font-size: 32rpx;
}

.form-group {
  margin-bottom: 32rpx;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.form-input {
  height: 88rpx;
  border-radius: 12rpx;
  background-color: #f5f5f5;
  padding: 0 28rpx;
  font-size: 28rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.game-types {
  display: flex;
  flex-wrap: nowrap;
  margin: 0 -4rpx;
  overflow-x: auto;
}

/* 为不同游戏类型添加特色背景 */
.game-type-item[data-type="mahjong"] {
  background: linear-gradient(135deg, rgba(230, 196, 108, 0.2), rgba(255, 255, 255, 0.9)) !important;
}

.game-type-item[data-type="poker"] {
  background: linear-gradient(135deg, rgba(230, 196, 108, 0.2), rgba(255, 255, 255, 0.9)) !important;
}

.game-type-item[data-type="board"] {
  background: linear-gradient(135deg, rgba(230, 196, 108, 0.2), rgba(255, 255, 255, 0.9)) !important;
}

.game-type-item[data-type="other"] {
  background: linear-gradient(135deg, rgba(230, 196, 108, 0.2), rgba(255, 255, 255, 0.9)) !important;
}

.game-type-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(25% - 8rpx);
  margin: 0 4rpx;
  padding: 18rpx 8rpx;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  flex-shrink: 0;
  box-sizing: border-box;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
}

.game-type-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.12);
}

.game-type-item.active {
  border-color: #d4af37;
  box-shadow: inset 0 0 0 1rpx #d4af37, 0 8rpx 24rpx rgba(212, 175, 55, 0.2);
  transform: scale(0.98);
}

/* 选中状态的特色背景覆盖 */
.game-type-item[data-type="mahjong"].active {
  background: linear-gradient(135deg, #e6c46c, #d4af37) !important;
}

.game-type-item[data-type="poker"].active {
  background: linear-gradient(135deg, #e6c46c, #d4af37) !important;
}

.game-type-item[data-type="board"].active {
  background: linear-gradient(135deg, #e6c46c, #d4af37) !important;
}

.game-type-item[data-type="other"].active {
  background: linear-gradient(135deg, #e6c46c, #d4af37) !important;
}

.game-type-item.active .type-name {
  color: #8B0000;
  font-weight: 600;
}

.type-icon {
  font-size: 34rpx;
  margin-bottom: 6rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.1));
}

.type-name {
  font-size: 18rpx;
  color: #333;
  text-align: center;
  font-weight: 500;
  line-height: 1.2;
}

.player-mode-selector {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.mode-item {
  display: flex;
  align-items: center;
  padding: 28rpx;
  background-color: #f5f5f5;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
}

.mode-item.active {
  background-color: rgba(212, 175, 55, 0.1);
  border-color: #d4af37;
}

.mode-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  width: 60rpx;
  text-align: center;
}

.mode-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.mode-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.mode-item.active .mode-name {
  color: #8B0000;
}

.mode-desc {
  font-size: 24rpx;
  color: #999;
}

.mode-item.active .mode-desc {
  color: #666;
}

.player-count-selector {
  display: flex;
}

.count-item {
  flex: 1;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  margin: 0 8rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
}

.count-item:first-child {
  margin-left: 0;
}

.count-item:last-child {
  margin-right: 0;
}

.count-item.active {
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
}

/* 自定义人数输入框样式 */
.custom-count-input {
  margin-top: 24rpx;
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.custom-count-input.show {
  opacity: 1;
  max-height: 150rpx; /* 调整最大高度 */
}

.custom-input {
  margin-bottom: 16rpx;
}

.input-tip {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  padding: 8rpx;
  background-color: #f8f4e9;
  border-radius: 6rpx;
  margin-bottom: 8rpx;
}

/* 底部间距 */
.bottom-spacer {
  height: 60rpx;
}

/* 底部固定导航栏 */
.bottom-nav-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: calc(70rpx + env(safe-area-inset-bottom)); /* 使用calc确保足够的高度并适配安全区域 */
  background-color: #ffffff;
  border-top: 1rpx solid #eeeeee;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx; /* 移除垂直内边距 */
  box-sizing: border-box;
  z-index: 100;
  /* 安全区域的适配现在由height处理 */
  box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.05);
}

.next-btn {
  width: 100%;
  height: 70rpx; /* 调整高度以小于容器 */
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
  border-radius: 32rpx; /* 调整圆角以保持形状 */
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8rpx 24rpx rgba(212, 175, 55, 0.3);
  letter-spacing: 4rpx;
  position: relative;
  overflow: hidden;
}

.next-btn::before {
  content: '';
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0));
  transform: rotate(15deg) translate(-100%, 0);
  transition: transform 0.6s ease;
}

.next-btn:active::before {
  transform: rotate(15deg) translate(100%, 0);
}

.next-btn[disabled] {
  background: #cccccc !important;
  color: #ffffff !important;
  opacity: 0.6;
}

button[disabled] {
  background-color: #cccccc !important;
  color: #ffffff !important;
  opacity: 0.6;
}

/* 游戏类型响应式优化 */
@media screen and (max-width: 600rpx) {
  .game-types {
    margin: 0 -2rpx;
  }
  
  .game-type-item {
    width: calc(25% - 4rpx);
    margin: 0 2rpx;
    padding: 10rpx 4rpx;
  }
  
  .type-icon {
    font-size: 28rpx;
    margin-bottom: 4rpx;
  }
  
  .type-name {
    font-size: 16rpx;
  }
} 