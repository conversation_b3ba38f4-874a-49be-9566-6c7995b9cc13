/**index.wxss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f4e9; /* 轻微的米色背景 */
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

/* 背景麻将装饰元素 */
.bg-elements {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none; /* 不阻挡点击事件 */
}

.mahjong-tile {
  position: absolute;
  width: 80rpx;
  height: 100rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.15);
  transform: rotate(var(--rotation));
  opacity: 0.1;
  z-index: -1;
}

.mahjong-1 {
  --rotation: 15deg;
  top: 10%;
  left: 10%;
}

.mahjong-2 {
  --rotation: -10deg;
  top: 20%;
  right: 15%;
}

.mahjong-3 {
  --rotation: 5deg;
  bottom: 25%;
  left: 5%;
}

.mahjong-4 {
  --rotation: -20deg;
  bottom: 15%;
  right: 10%;
}

.welcome-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx;
  margin-top: 44px; /* 状态栏高度 */
  position: relative;
  z-index: 10;
}

.logo-container {
  margin: 64rpx 0 32rpx;
  position: relative;
}

.logo {
  width: 180rpx;
  height: 180rpx;
  border-radius: 50%;
  box-shadow: 0 8rpx 16rpx rgba(0,0,0,0.12);
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 64rpx;
}

.mahjong-title-box {
  position: relative;
  padding: 16rpx 32rpx;
  margin-bottom: 16rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  border-radius: 12rpx;
  box-shadow: 0 6rpx 16rpx rgba(212, 175, 55, 0.3);
  transform: rotate(-2deg);
}

.title {
  font-size: 56rpx;
  font-weight: 600;
  color: #8B0000; /* 深红色 */
  text-shadow: 2rpx 2rpx 4rpx rgba(0,0,0,0.2);
  letter-spacing: 6rpx;
  font-family: "Microsoft YaHei", sans-serif;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  margin-top: 12rpx;
}

.features {
  width: 100%;
  margin-bottom: 64rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  background-color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.08);
  border-left: 8rpx solid #d4af37; /* 金色边框 */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.08);
}

.feature-icon {
  margin-right: 24rpx;
}

.mahjong-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
  font-size: 40rpx;
  font-weight: bold;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.15);
}

.feature-text {
  display: flex;
  flex-direction: column;
}

.feature-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #666;
}

.action-buttons {
  width: 100%;
  padding: 0 32rpx;
  margin-top: 32rpx;
}

.action-buttons .btn {
  height: 96rpx;
  font-size: 36rpx;
  border-radius: 48rpx;
  background: linear-gradient(135deg, #e6c46c, #d4af37);
  color: #8B0000;
  font-weight: 600;
  box-shadow: 0 8rpx 16rpx rgba(212, 175, 55, 0.3);
  border: none;
  letter-spacing: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.action-buttons .btn:before {
  content: '';
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  right: -10rpx;
  bottom: -10rpx;
  background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0));
  transform: rotate(15deg) translate(-100%, 0);
  transition: transform 0.6s ease;
}

.action-buttons .btn:active:before {
  transform: rotate(15deg) translate(100%, 0);
}
