/* components/custom-navbar/custom-navbar.wxss */
.custom-navbar-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: #ffffff;
  z-index: 9999;
}

.status-bar {
  width: 100%;
  background-color: inherit;
}

.custom-navbar-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;
}

/* 胶囊按钮组 - 模仿微信原生胶囊样式 */
.custom-nav-capsule {
  position: absolute;
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  z-index: 10000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
  border-radius: 32rpx;
}

.custom-nav-capsule:active {
  background-color: rgba(255, 255, 255, 0.6);
  transform: scale(0.98);
}

/* 胶囊按钮 */
.capsule-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border-radius: 16rpx;
}

.capsule-btn:active {
  background-color: rgba(0, 0, 0, 0.1);
}

/* 按钮图标 - 与微信原生胶囊图标颜色一致 */
.btn-icon {
  font-size: 36rpx;
  font-weight: 400;
  color: #000000;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.9;
}

.back-icon {
  font-size: 38rpx;
  font-weight: 300;
}

.home-icon {
  font-size: 34rpx;
  font-weight: 400;
}

/* 分隔线 - 与微信原生胶囊分隔线一致 */
.capsule-divider {
  width: 1rpx;
  background-color: rgba(0, 0, 0, 0.2);
  margin: 0 2rpx;
}

/* 标题 */
.nav-title {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  text-align: center;
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  pointer-events: none;
}

/* 右侧插槽 */
.nav-right {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
}

/* 深色模式适配 - 保持与微信原生胶囊一致 */
@media (prefers-color-scheme: dark) {
  .custom-navbar-box {
    background-color: #000000;
  }

  .custom-nav-capsule {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.2);
  }

  .custom-nav-capsule:active {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .capsule-btn:active {
    background-color: rgba(255, 255, 255, 0.2);
  }

  .btn-icon {
    color: #ffffff;
    opacity: 0.9;
  }

  .capsule-divider {
    background-color: rgba(255, 255, 255, 0.3);
  }

  .nav-title {
    color: #ffffff;
  }
}

/* 响应式适配 - 保持与微信原生胶囊的相对位置 */
@media screen and (max-width: 750rpx) {
  .nav-right {
    right: 20rpx;
  }

  .btn-icon {
    font-size: 34rpx;
  }

  .back-icon {
    font-size: 36rpx;
  }

  .home-icon {
    font-size: 32rpx;
  }
}
